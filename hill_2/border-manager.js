// === BORDER AND BOUNDARY MANAGEMENT ===
// Moved from manager.js to keep it organized

// ===== CONSTANTS AND CONFIGURATION =====
const ZOOM_THRESHOLD_COUNTRY = 5; // < 5: chỉ border quốc gia
const ZOOM_THRESHOLD_PROVINCE = 8; // > 8: chi tiết commune
const VN_PROVINCE_GEOJSON_PATH = '/vn_province_geojson_final/';
const VN_PROVINCE_TILES_URL = 'vn_province_borders/vietnam_province_borders_tiles/{z}/{x}/{y}.pbf';
const VN_COMMUNE_TILES_URL = 'vn_commune_borders/vietnam_commune/{z}/{x}/{y}.pbf';

// ===== STATE MANAGEMENT =====
let vietnamBordersLoaded = false;
let currentZoomLevel = 0;
let currentBorderType = null; // 'province' hoặc 'commune'
let loadedProvinces = new Set(); // Track đã load province nào
let bordersInitialized = false;
let lastBorderUpdate = 0;

// ===== VIETNAM PROVINCE DATA =====
// Danh sách các file geojson tỉnh/thành Việt Nam với bounds (match với files thực tế)
const vietnamProvinceData = {
    '101_Tp_Ha_Noi.geojson': {
        code: '101', name: 'Hà Nội',
        bounds: [105.3, 20.8, 105.9, 21.4]
    },
    '103_Tp_Hai_Phong.geojson': {
        code: '103', name: 'Hải Phòng',
        bounds: [106.4, 20.7, 107.1, 21.1]
    },
    '109_Tinh_Hung_Yen.geojson': {
        code: '109', name: 'Hưng Yên',
        bounds: [105.8, 20.8, 106.3, 21.2]
    },
    '117_Tinh_Ninh_Binh.geojson': {
        code: '117', name: 'Ninh Bình',
        bounds: [105.7, 20.0, 106.3, 20.5]
    },
    '203_Tinh_Cao_Bang.geojson': {
        code: '203', name: 'Cao Bằng',
        bounds: [105.8, 22.3, 106.9, 23.0]
    },
    '205_Tinh_Lao_Cai.geojson': {
        code: '205', name: 'Lào Cai',
        bounds: [103.6, 22.1, 104.5, 23.0]
    },
    '209_Tinh_Lang_Son.geojson': {
        code: '209', name: 'Lạng Sơn',
        bounds: [106.4, 21.7, 107.0, 22.4]
    },
    '211_Tinh_Tuyen_Quang.geojson': {
        code: '211', name: 'Tuyên Quang',
        bounds: [105.1, 21.7, 105.9, 22.4]
    },
    '215_Tinh_Thai_Nguyen.geojson': {
        code: '215', name: 'Thái Nguyên',
        bounds: [105.6, 21.3, 106.2, 22.0]
    },
    '217_Tinh_Phu_Tho.geojson': {
        code: '217', name: 'Phú Thọ',
        bounds: [104.9, 21.2, 105.6, 21.8]
    },
    '223_Tinh_Bac_Ninh.geojson': {
        code: '223', name: 'Bắc Ninh',
        bounds: [106.0, 21.0, 106.3, 21.3]
    },
    '225_Tinh_Quang_Ninh.geojson': {
        code: '225', name: 'Quảng Ninh',
        bounds: [106.8, 20.8, 108.4, 21.8]
    },
    '301_Tinh_Dien_Bien.geojson': {
        code: '301', name: 'Điện Biên',
        bounds: [103.0, 21.2, 103.8, 22.0]
    },
    '302_Tinh_Lai_Chau.geojson': {
        code: '302', name: 'Lai Châu',
        bounds: [103.0, 22.0, 103.8, 22.8]
    },
    '303_Tinh_Son_La.geojson': {
        code: '303', name: 'Sơn La',
        bounds: [103.5, 20.8, 104.8, 22.2]
    },
    '401_Tinh_Thanh_Hoa.geojson': {
        code: '401', name: 'Thanh Hóa',
        bounds: [105.1, 19.4, 106.3, 20.9]
    },
    '403_Tinh_Nghe_An.geojson': {
        code: '403', name: 'Nghệ An',
        bounds: [104.1, 18.6, 105.9, 19.9]
    },
    '405_Tinh_Ha_Tinh.geojson': {
        code: '405', name: 'Hà Tĩnh',
        bounds: [105.4, 18.0, 106.3, 18.8]
    },
    '409_Tinh_Quang_Tri.geojson': {
        code: '409', name: 'Quảng Trị',
        bounds: [106.8, 16.6, 107.6, 17.2]
    },
    '411_Tp_Hue.geojson': {
        code: '411', name: 'Thừa Thiên Huế',
        bounds: [107.2, 16.0, 108.2, 16.8]
    },
    '501_Tp_Da_Nang.geojson': {
        code: '501', name: 'Đà Nẵng',
        bounds: [107.9, 15.9, 108.3, 16.2]
    },
    '505_Tinh_Quang_Ngai.geojson': {
        code: '505', name: 'Quảng Ngãi',
        bounds: [108.4, 14.1, 109.5, 15.4]
    },
    '511_Tinh_Khanh_Hoa.geojson': {
        code: '511', name: 'Khánh Hòa',
        bounds: [108.4, 11.6, 109.5, 12.8]
    },
    '603_Tinh_Gia_Lai.geojson': {
        code: '603', name: 'Gia Lai',
        bounds: [107.7, 13.2, 108.8, 14.7]
    },
    '605_Tinh_Dak_Lak.geojson': {
        code: '605', name: 'Đắk Lắk',
        bounds: [107.2, 12.1, 108.7, 13.2]
    },
    '701_Tp_Ho_Chi_Minh.geojson': {
        code: '701', name: 'TP Hồ Chí Minh',
        bounds: [106.3, 10.3, 107.0, 11.2]
    },
    '703_Tinh_Lam_Dong.geojson': {
        code: '703', name: 'Lâm Đồng',
        bounds: [107.1, 10.5, 108.7, 12.2]
    },
    '709_Tinh_Tay_Ninh.geojson': {
        code: '709', name: 'Tây Ninh',
        bounds: [105.8, 11.0, 106.8, 11.8]
    },
    '713_Tinh_Dong_Nai.geojson': {
        code: '713', name: 'Đồng Nai',
        bounds: [106.8, 10.4, 107.6, 11.5]
    },
    '803_Tinh_Dong_Thap.geojson': {
        code: '803', name: 'Đồng Tháp',
        bounds: [105.3, 10.3, 106.0, 11.0]
    },
    '805_Tinh_An_Giang.geojson': {
        code: '805', name: 'An Giang',
        bounds: [104.7, 10.2, 105.6, 10.9]
    },
    '809_Tinh_Vinh_Long.geojson': {
        code: '809', name: 'Vĩnh Long',
        bounds: [105.8, 9.8, 106.4, 10.4]
    },
    '815_Tp_Can_Tho.geojson': {
        code: '815', name: 'Cần Thơ',
        bounds: [105.6, 9.9, 105.9, 10.2]
    },
    '823_Tinh_Ca_Mau.geojson': {
        code: '823', name: 'Cà Mau',
        bounds: [104.6, 8.4, 105.8, 9.6]
    }
};

// Helper functions
const vietnamProvinceFiles = Object.keys(vietnamProvinceData);

// ===== GEOJSON LOADING FUNCTIONS =====

// Hàm load một file geojson tỉnh/thành
async function loadProvinceGeojson(filename) {
    try {
        console.log(`🔄 Attempting to load: ${VN_PROVINCE_GEOJSON_PATH}${filename}`);
        const response = await fetch(`${VN_PROVINCE_GEOJSON_PATH}${filename}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const geojsonData = await response.json();
        console.log(`✅ Successfully loaded: ${filename}`);
        return geojsonData;
    } catch (error) {
        console.error(`❌ Error loading ${filename}:`, error);
        return null;
    }
}

// Hàm kiểm tra bbox overlap
function bboxOverlap(bbox1, bbox2) {
    const [west1, south1, east1, north1] = bbox1;
    const [west2, south2, east2, north2] = bbox2;

    return !(east1 < west2 || west1 > east2 || north1 < south2 || south1 > north2);
}

// Hàm lấy provinces trong viewport
function getProvincesInViewport() {
    const bounds = map.getBounds();
    const viewportBbox = [
        bounds.getWest(),
        bounds.getSouth(),
        bounds.getEast(),
        bounds.getNorth()
    ];

    const provincesInView = [];
    for (const [filename, data] of Object.entries(vietnamProvinceData)) {
        if (bboxOverlap(viewportBbox, data.bounds)) {
            provincesInView.push({
                filename,
                code: data.code,
                name: data.name,
                bounds: data.bounds
            });
        }
    }

    return provincesInView;
}

// Hàm thêm border tỉnh/thành Việt Nam từ local geojson files
async function addVietnamProvinceBorders() {
    if (!map.isStyleLoaded()) {
        console.log('❌ Map style not loaded yet, skipping Vietnam province borders');
        return;
    }

    console.log('🇻🇳 Loading Vietnam province borders from local geojson files...');
    console.log('🔍 Current zoom level:', map.getZoom());

    try {
        const provincesInView = getProvincesInViewport();
        console.log(`Found ${provincesInView.length} provinces in viewport`);

        // Load và add từng tỉnh/thành trong viewport
        for (const province of provincesInView) {
            const { filename, code, name } = province;
            const sourceId = `vn-province-${code}`;
            const layerId = `vn-province-border-${code}`;

            // Kiểm tra nếu source đã tồn tại
            if (map.getSource(sourceId)) {
                // Nếu đã có source, chỉ cần hiển thị layer
                if (map.getLayer(layerId)) {
                    map.setLayoutProperty(layerId, 'visibility', 'visible');
                }
                continue;
            }

            const geojsonData = await loadProvinceGeojson(filename);
            if (!geojsonData) continue;

            // Add source
            map.addSource(sourceId, {
                type: 'geojson',
                data: geojsonData
            });

            // Add layer
            map.addLayer({
                id: layerId,
                type: 'line',
                source: sourceId,
                paint: {
                    'line-color': '#ff6b6b',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        5, 1.5,
                        8, 2.0,
                        12, 2.5
                    ],
                    'line-opacity': 0.8
                }
            });

            console.log(`✅ Added Vietnam province border: ${name} (${code})`);
            loadedProvinces.add(code);
        }

        vietnamBordersLoaded = true;
        console.log('🇻🇳 Vietnam province borders loaded successfully');

    } catch (error) {
        console.error('Error loading Vietnam province borders:', error);
    }
}

// Hàm ẩn Vietnam province borders
function hideVietnamProvinceBorders() {
    try {
        for (const code of loadedProvinces) {
            const layerId = `vn-province-border-${code}`;
            if (map.getLayer(layerId)) {
                map.setLayoutProperty(layerId, 'visibility', 'none');
            }
        }
        console.log('🇻🇳 Hidden Vietnam province borders');
    } catch (error) {
        console.error('Error hiding Vietnam province borders:', error);
    }
}

// ===== LAYER CONTROL MENU =====
let layerControlMenu = null;

// Tạo menu control cho tất cả layers
function createLayerControlMenu() {
    // Xóa menu cũ nếu có
    if (layerControlMenu) {
        document.body.removeChild(layerControlMenu);
    }

    // Tạo menu container
    layerControlMenu = document.createElement('div');
    layerControlMenu.id = 'layer-control-menu';
    layerControlMenu.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: white;
        border: 1px solid #ccc;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        z-index: 1000;
        font-family: Arial, sans-serif;
        font-size: 12px;
        max-width: 300px;
        max-height: 80vh;
        overflow-y: auto;
    `;

    // Header
    const header = document.createElement('div');
    header.innerHTML = '<strong>🗺️ Layer Controls</strong>';
    header.style.cssText = 'margin-bottom: 10px; padding-bottom: 5px; border-bottom: 1px solid #eee; font-size: 14px;';
    layerControlMenu.appendChild(header);

    // Lấy tất cả layers
    const layers = map.getStyle().layers;
    console.log('📋 All layers:', layers.map(l => l.id));

    // Nhóm layers theo category
    const layerCategories = {
        'Background': ['Background'],
        'Terrain': ['Hillshade', 'Landcover', 'Land outline'],
        'Water': ['River', 'Water shadow', 'Water', 'Ocean labels', 'Sea labels', 'Lakeline labels'],
        'Transport': ['Aeroway', 'Tunnel path', 'Tunnel', 'Railway tunnel', 'Railway tunnel dash', 'Pier', 'Pier road', 'Bridge', 'Road network', 'Path minor', 'Path', 'Railway', 'Railway dash'],
        'Buildings': ['Building', 'Building top', 'Residential'],
        'Borders': ['Other border', 'Other border dash', 'Disputed border', 'Country border', 'state-boundaries', 'country-boundaries'],
        'Labels': ['Road labels', 'Place labels', 'Village labels', 'Town labels', 'State labels', 'City labels', 'Country labels', 'Continent labels'],
        'Effects': ['hillshade-shadows'],
        'Vietnam': ['vn-province-borders', 'vn-commune-borders']
    };

    // Tạo sections cho từng category
    Object.entries(layerCategories).forEach(([category, layerIds]) => {
        const section = document.createElement('div');
        section.style.cssText = 'margin-bottom: 15px;';
        
        // Category header với toggle all
        const categoryHeader = document.createElement('div');
        categoryHeader.style.cssText = 'margin-bottom: 5px; font-size: 13px; color: #333; display: flex; justify-content: space-between; align-items: center;';
        
        const categoryTitle = document.createElement('span');
        categoryTitle.innerHTML = `<strong>${category}:</strong>`;
        
        const toggleAllCheckbox = document.createElement('input');
        toggleAllCheckbox.type = 'checkbox';
        toggleAllCheckbox.checked = true;
        toggleAllCheckbox.style.cssText = 'margin-left: 10px;';
        toggleAllCheckbox.title = `Toggle all ${category} layers`;
        
        // Toggle all functionality
        toggleAllCheckbox.addEventListener('change', (checked) => {
            const checkboxes = section.querySelectorAll('input[type="checkbox"]:not([title*="Toggle all"])');
            checkboxes.forEach(cb => {
                cb.checked = toggleAllCheckbox.checked;
                // Trigger change event
                const event = new Event('change');
                cb.dispatchEvent(event);
            });
        });
        
        categoryHeader.appendChild(categoryTitle);
        categoryHeader.appendChild(toggleAllCheckbox);
        section.appendChild(categoryHeader);

        // Tạo checkboxes cho từng layer trong category
        layerIds.forEach(layerId => {
            const layer = layers.find(l => l.id === layerId);
            if (layer) {
                const checkbox = createLayerCheckbox(layerId, true, (checked) => {
                    try {
                        if (checked) {
                            map.setLayoutProperty(layerId, 'visibility', 'visible');
                            console.log(`✅ Showed layer: ${layerId}`);
                            
                            // Đặc biệt xử lý cho Other border - thêm filter loại trừ VN
                            if (layerId === 'Other border') {
                                hideOtherBorderForVietnam();
                            }
                        } else {
                            map.setLayoutProperty(layerId, 'visibility', 'none');
                            console.log(`❌ Hidden layer: ${layerId}`);
                        }
                    } catch (e) {
                        console.log(`Could not toggle layer ${layerId}:`, e.message);
                    }
                });
                section.appendChild(checkbox);
            }
        });

        layerControlMenu.appendChild(section);
    });

    // Auto mode section
    const autoSection = document.createElement('div');
    autoSection.innerHTML = '<strong>🔄 Auto Mode:</strong>';
    autoSection.style.cssText = 'margin-top: 15px; padding-top: 10px; border-top: 1px solid #eee;';
    
    const autoCheckbox = createCheckbox('Auto switch borders by zoom', true, (checked) => {
        if (checked) {
            // Enable auto mode
            map.on('zoom', debouncedHandleZoomChange);
            map.on('zoomend', debouncedHandleZoomChange);
            debouncedHandleZoomChange(); // Trigger once
        } else {
            // Disable auto mode
            map.off('zoom', debouncedHandleZoomChange);
            map.off('zoomend', debouncedHandleZoomChange);
        }
    });
    
    autoSection.appendChild(autoCheckbox);
    layerControlMenu.appendChild(autoSection);

    // Current status
    const statusSection = document.createElement('div');
    statusSection.style.cssText = 'margin-top: 10px; padding-top: 5px; border-top: 1px solid #eee; font-size: 11px; color: #666;';
    
    const updateStatus = () => {
        const zoom = map.getZoom();
        const currentBorder = currentBorderType || 'none';
        const visibleLayers = layers.filter(l => {
            try {
                return map.getLayoutProperty(l.id, 'visibility') !== 'none';
            } catch (e) {
                return true;
            }
        }).length;
        
        statusSection.innerHTML = `
            <strong>📊 Status:</strong><br>
            Zoom: ${zoom.toFixed(1)}<br>
            Current border: ${currentBorder}<br>
            Visible layers: ${visibleLayers}/${layers.length}<br>
            VN borders loaded: ${vietnamBordersLoaded}
        `;
    };
    
    updateStatus();
    layerControlMenu.appendChild(statusSection);

    // Thêm vào body
    document.body.appendChild(layerControlMenu);

    // Update status mỗi giây
    setInterval(updateStatus, 1000);

    console.log('🗺️ Layer control menu created with', layers.length, 'layers');
}

// Helper function để tạo checkbox cho layer
function createLayerCheckbox(layerId, checked, onChange) {
    const container = document.createElement('div');
    container.style.cssText = 'margin: 3px 0; display: flex; align-items: center;';
    
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.checked = checked;
    checkbox.style.cssText = 'margin-right: 6px;';
    
    const labelElement = document.createElement('span');
    labelElement.textContent = layerId;
    labelElement.style.cssText = 'font-size: 11px; cursor: pointer;';
    
    // Click vào label cũng toggle checkbox
    labelElement.addEventListener('click', () => {
        checkbox.checked = !checkbox.checked;
        onChange(checkbox.checked);
    });
    
    checkbox.addEventListener('change', () => {
        onChange(checkbox.checked);
    });
    
    container.appendChild(checkbox);
    container.appendChild(labelElement);
    
    return container;
}

// Helper function để tạo checkbox
function createCheckbox(label, checked, onChange) {
    const container = document.createElement('div');
    container.style.cssText = 'margin: 5px 0; display: flex; align-items: center;';
    
    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.checked = checked;
    checkbox.style.cssText = 'margin-right: 8px;';
    
    const labelElement = document.createElement('span');
    labelElement.textContent = label;
    labelElement.style.cssText = 'font-size: 13px;';
    
    checkbox.addEventListener('change', () => {
        onChange(checkbox.checked);
    });
    
    container.appendChild(checkbox);
    container.appendChild(labelElement);
    
    return container;
}

// Hàm ẩn/hiện country borders MapTiler
function hideMapTilerCountryBorders() {
    try {
        if (map.getLayer('country-boundaries')) {
            map.setLayoutProperty('country-boundaries', 'visibility', 'none');
            console.log('🗺️ Hidden MapTiler country borders');
        }
    } catch (e) {
        console.log('Could not hide MapTiler country borders:', e.message);
    }
}

function showMapTilerCountryBorders() {
    try {
        if (map.getLayer('country-boundaries')) {
            map.setLayoutProperty('country-boundaries', 'visibility', 'visible');
            console.log('🗺️ Shown MapTiler country borders');
        }
    } catch (e) {
        console.log('Could not show MapTiler country borders:', e.message);
    }
}

// ===== HÀM CHÍNH ĐỂ ĐẢMBẢO RANH GIỚI LUÔN HIỂN THỊ =====
function ensureBoundariesOnTop() {
    if (!map.isStyleLoaded()) return;

    const layers = map.getStyle().layers;
    
    // Tìm TẤT CẢ layer có thể là ranh giới hoặc nhãn
    const boundaryAndLabelLayers = layers.filter(layer => {
        const id = layer.id.toLowerCase();
        const type = layer.type;
        
        // Điều kiện mở rộng để bắt tất cả các loại ranh giới và nhãn
        return (
            // Các layer ranh giới
            id.includes('boundary') || 
            id.includes('admin') || 
            id.includes('border') || 
            id.includes('country') || 
            id.includes('state') || 
            id.includes('province') ||
            id.includes('outline') ||
            id.includes('stroke') ||
            // Các layer nhãn
            type === 'symbol' ||
            id.includes('label') || 
            id.includes('text') || 
            id.includes('place') ||
            id.includes('city') ||
            id.includes('town') ||
            // Các layer line có thể là ranh giới
            (type === 'line' && (
                id.includes('line') || 
                id.includes('admin') || 
                id.includes('boundary')
            ))
        );
    });

    // Di chuyển tất cả lên trên cùng, giữ nguyên thứ tự tương đối
    boundaryAndLabelLayers.forEach(layer => {
        try {
            if (map.getLayer(layer.id)) {
                // Xóa fill color cho các layer fill/polygon
                if (layer.type === 'fill') {
                    map.setPaintProperty(layer.id, 'fill-opacity', 0);
                    map.setPaintProperty(layer.id, 'fill-color', 'transparent');
                    // Đảm bảo stroke hiển thị
                    if (map.getPaintProperty(layer.id, 'fill-outline-color')) {
                        map.setPaintProperty(layer.id, 'fill-outline-color', '#666666');
                    }
                }
                
                map.moveLayer(layer.id);
            }
        } catch (e) {
            console.log(`Failed to move layer ${layer.id}:`, e.message);
        }
    });
}

// Tạo Hàm riêng để đảm bảo borders luôn ở trên - với kiểm tra để tránh spam
function ensureAllBordersOnTop() {
    if (!mapLoaded || !map.isStyleLoaded()) return;

    setTimeout(() => {
        ensureBoundariesOnTop();
        addCustomBorderLayers();
        console.log('✅ All borders moved to top');
    }, 100);
}

// ===== HÀM THÊM BORDER LAYER RIÊNG =====
function addCustomBorderLayers() {
    if (!map.isStyleLoaded()) return;
    
    try {
        // Thêm source cho ranh giới từ MapTiler Countries
        if (!map.getSource('maptiler-countries')) {
            map.addSource('maptiler-countries', {
                type: 'vector',
                url: `https://api.maptiler.com/tiles/countries/tiles.json?key=${maptilersdk.config.apiKey}`
            });
            console.log('Added MapTiler Countries source');
        }

        // 1. Thêm ranh giới quốc gia (admin_level=2) - LOẠI BỎ VIETNAM
        if (!map.getLayer('country-boundaries')) {
            map.addLayer({
                id: 'country-boundaries',
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: [
                    'all',
                    ['==', 'admin_level', 2],
                    ['!=', 'iso_a2', 'VN'] // LOẠI BỎ VIETNAM
                ],
                paint: {
                    'line-color': '#444444',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        0, 0.5,
                        4, 1,
                        8, 1.5,
                        12, 2
                    ],
                    'line-opacity': 0.8
                }
            });
            console.log('Added country boundaries layer (excluding Vietnam)');
        }

        // 2. Thêm ranh giới cấp 1 (admin_level=4 - states/provinces)
        // LUÔN HIỂN THỊ cho tất cả countries, NHƯNG FILTER OUT Vietnam
        if (!map.getLayer('state-boundaries')) {
            map.addLayer({
                id: 'state-boundaries',
                type: 'line',
                source: 'maptiler-countries',
                'source-layer': 'boundary',
                filter: [
                    'all',
                    ['==', 'admin_level', 4],
                    ['!=', 'iso_a2', 'VN'] // LOẠI BỎ VIETNAM provinces
                ],
                paint: {
                    'line-color': '#666666',
                    'line-width': [
                        'interpolate',
                        ['linear'],
                        ['zoom'],
                        4, 0.3,
                        8, 0.8,
                        12, 1.2
                    ],
                    'line-opacity': 0.6,
                    'line-dasharray': [2, 2]
                }
            });
            console.log('Added state boundaries layer (MapTiler - excluding Vietnam)');
        }

        // 3. Đảm bảo các border layer luôn ở trên cùng
        setTimeout(() => {
            if (map.getLayer('state-boundaries')) {
                map.moveLayer('state-boundaries');
            }
            if (map.getLayer('country-boundaries')) {
                map.moveLayer('country-boundaries');
            }
        }, 100);

    } catch (error) {
        console.error('Error adding custom border layers:', error);
    }
}

// Hàm test tiles URL
function testTilesURL() {
    const currentZoom = map.getZoom();
    const center = map.getCenter();
    const tileCoords = getTileCoordinates(center.lng, center.lat, Math.floor(currentZoom));
    
    console.log('🧪 Testing tiles URL:');
    console.log('  - Current zoom:', currentZoom);
    console.log('  - Center:', center);
    console.log('  - Tile coordinates:', tileCoords);
    
    const testURL = VN_PROVINCE_TILES_URL
        .replace('{z}', tileCoords.z)
        .replace('{x}', tileCoords.x)
        .replace('{y}', tileCoords.y);
    
    console.log('  - Test URL:', testURL);
    
    // Test fetch
    fetch(testURL)
        .then(response => {
            console.log('  - Response status:', response.status);
            console.log('  - Response headers:', response.headers);
            if (response.ok) {
                console.log('  - ✅ Tile exists and accessible');
            } else {
                console.log('  - ❌ Tile not found or not accessible');
            }
        })
        .catch(error => {
            console.log('  - ❌ Fetch error:', error);
        });
}

// Helper function để tính tile coordinates
function getTileCoordinates(lng, lat, zoom) {
    const n = Math.pow(2, zoom);
    const xtile = Math.floor((lng + 180) / 360 * n);
    const ytile = Math.floor((1 - Math.log(Math.tan(lat * Math.PI / 180) + 1 / Math.cos(lat * Math.PI / 180)) / Math.PI) / 2 * n);
    return { x: xtile, y: ytile, z: zoom };
}

// Hàm thêm Vietnam province borders (zoom 5-8)
function addVietnamProvinceTiles() {
    console.log('🇻🇳 addVietnamProvinceTiles CALLED - Zoom:', map.getZoom());
    
    // Nếu đã load commune tiles, remove trước
    if (map.getLayer('vn-commune-borders')) {
        map.removeLayer('vn-commune-borders');
        console.log('🇻🇳 Removed commune borders layer');
    }
    if (map.getSource('vn-communes')) {
        map.removeSource('vn-communes');
        console.log('🇻🇳 Removed commune tiles source');
    }

    // Add province tiles source
    if (!map.getSource('vn-provinces')) {
        console.log('🇻🇳 Adding province tiles source');
        map.addSource('vn-provinces', {
            type: 'vector',
            tiles: [VN_PROVINCE_TILES_URL],
            minzoom: 5,
            maxzoom: 12
        });
        console.log('🇻🇳 Added Vietnam province tiles source');
    } else {
        console.log('🇻🇳 Province tiles source already exists');
    }

    // Add province borders layer
    if (!map.getLayer('vn-province-borders')) {
        console.log('🇻🇳 Adding province borders layer');
        map.addLayer({
            id: 'vn-province-borders',
            type: 'line',
            source: 'vn-provinces',
            'source-layer': 'provinces',
            paint: {
                'line-color': '#888888',
                'line-width': [
                    'interpolate',
                    ['linear'],
                    ['zoom'],
                    5, 0.8,
                    8, 1.2,
                    12, 1.8
                ],
                'line-opacity': 0.7
            }
        });
        console.log('🇻🇳 Added Vietnam province borders layer');
    } else {
        console.log('🇻🇳 Province borders layer already exists');
    }

    // Move to top
    setTimeout(() => {
        if (map.getLayer('vn-province-borders')) {
            map.moveLayer('vn-province-borders');
            console.log('🇻🇳 Moved province borders to top');
        }
    }, 100);

    currentBorderType = 'province';
    vietnamBordersLoaded = true;
    console.log('🇻🇳 Province borders setup complete');
}

// Hàm thêm Vietnam commune borders (zoom > 8)
function addVietnamCommuneTiles() {
    console.log('🇻🇳 addVietnamCommuneTiles CALLED - Zoom:', map.getZoom());
    
    // Nếu đã load province tiles, remove trước
    if (map.getLayer('vn-province-borders')) {
        map.removeLayer('vn-province-borders');
    }
    if (map.getSource('vn-provinces')) {
        map.removeSource('vn-provinces');
    }

    // Add commune tiles source
    if (!map.getSource('vn-communes')) {
        map.addSource('vn-communes', {
            type: 'vector',
            tiles: [VN_COMMUNE_TILES_URL],
            minzoom: 5,
            maxzoom: 12
        });
        console.log('🇻🇳 Added Vietnam commune tiles source');
    }

    // Add commune borders layer
    if (!map.getLayer('vn-commune-borders')) {
        map.addLayer({
            id: 'vn-commune-borders',
            type: 'line',
            source: 'vn-communes',
            'source-layer': 'provinces',
            paint: {
                'line-color': '#666666',
                'line-width': [
                    'interpolate',
                    ['linear'],
                    ['zoom'],
                    8, 0.6,
                    10, 0.8,
                    12, 1.0
                ],
                'line-opacity': 0.6
            }
        });
        console.log('🇻🇳 Added Vietnam commune borders layer');
    }

    // Move to top
    setTimeout(() => {
        if (map.getLayer('vn-commune-borders')) {
            map.moveLayer('vn-commune-borders');
        }
    }, 100);

    currentBorderType = 'commune';
    vietnamBordersLoaded = true;
}

// Hàm ẩn tất cả Vietnam borders
function hideVietnamBorders() {
    try {
        if (map.getLayer('vn-province-borders')) {
            map.removeLayer('vn-province-borders');
        }
        if (map.getSource('vn-provinces')) {
            map.removeSource('vn-provinces');
        }
        if (map.getLayer('vn-commune-borders')) {
            map.removeLayer('vn-commune-borders');
        }
        if (map.getSource('vn-communes')) {
            map.removeSource('vn-communes');
        }
        vietnamBordersLoaded = false;
        currentBorderType = null;
        console.log('🇻🇳 Hidden all Vietnam borders');
    } catch (e) {
        console.log('Could not hide Vietnam borders:', e.message);
    }
}

// Hàm ẩn TẤT CẢ borders của Vietnam từ MapTiler
function hideAllMapTilerVietnamBorders() {
    try {
        // Lấy tất cả layers hiện tại
        const layers = map.getStyle().layers;
        
        layers.forEach(layer => {
            const layerId = layer.id;
            
            // Kiểm tra nếu layer có filter và có thể chứa Vietnam borders
            if (layer.source && layer['source-layer'] && layer.filter) {
                try {
                    // Thêm filter loại trừ Vietnam nếu chưa có
                    const currentFilter = layer.filter;
                    let hasVietnamExclusion = false;
                    
                    // Kiểm tra xem đã có filter loại trừ Vietnam chưa
                    if (Array.isArray(currentFilter)) {
                        hasVietnamExclusion = currentFilter.some(condition => 
                            Array.isArray(condition) && 
                            condition[0] === '!=' && 
                            condition[1] === 'iso_a2' && 
                            condition[2] === 'VN'
                        );
                    }
                    
                    // Nếu chưa có, thêm vào
                    if (!hasVietnamExclusion) {
                        const newFilter = [
                            'all',
                            currentFilter,
                            ['!=', 'iso_a2', 'VN']
                        ];
                        map.setFilter(layerId, newFilter);
                        console.log(`🇻🇳 Added Vietnam exclusion to layer: ${layerId}`);
                    }
                } catch (e) {
                    console.log(`Could not update filter for layer ${layerId}:`, e.message);
                }
            }
        });
        
        console.log('🇻🇳 Hidden all MapTiler Vietnam borders');
    } catch (e) {
        console.log('Could not hide MapTiler Vietnam borders:', e.message);
    }
}

// Hàm hiển thị lại Other border cho non-VN
function showOtherBorderForNonVN() {
    try {
        console.log('🗺️ Showing Other border for non-VN countries...');
        
        // Hiển thị "Other border" layer
        const otherBorderLayer = map.getLayer('Other border');
        if (otherBorderLayer) {
            console.log('🗺️ Found Other border layer, setting visibility to visible');
            map.setLayoutProperty('Other border', 'visibility', 'visible');
        }
        
        // Hiển thị "Other border dash" layer
        const otherBorderDashLayer = map.getLayer('Other border dash');
        if (otherBorderDashLayer) {
            console.log('🗺️ Found Other border dash layer, setting visibility to visible');
            map.setLayoutProperty('Other border dash', 'visibility', 'visible');
        }
        
        console.log('🗺️ Shown Other border layers for non-VN');
        
    } catch (e) {
        console.log('Could not show Other border for non-VN:', e.message);
    }
}

// Hàm ẩn "Other border" (province borders) cho Vietnam
function hideOtherBorderForVietnam() {
    try {
        console.log('🇻🇳 Force hiding Other border for Vietnam...');
        
        // Tìm và ẩn "Other border" layer
        const otherBorderLayer = map.getLayer('Other border');
        if (otherBorderLayer) {
            console.log('🇻🇳 Found Other border layer, setting visibility to none');
            map.setLayoutProperty('Other border', 'visibility', 'none');
        } else {
            console.log('❌ Other border layer not found');
        }
        
        // Tìm và ẩn "Other border dash" layer (có thể là commune)
        const otherBorderDashLayer = map.getLayer('Other border dash');
        if (otherBorderDashLayer) {
            console.log('🇻🇳 Found Other border dash layer, setting visibility to none');
            map.setLayoutProperty('Other border dash', 'visibility', 'none');
        } else {
            console.log('❌ Other border dash layer not found');
        }
        
        console.log('🇻🇳 Hidden Other border layers for Vietnam');
        
    } catch (e) {
        console.log('Could not hide Other border for Vietnam:', e.message);
    }
}

// Hàm áp dụng filter loại trừ VN cho tất cả border layers
function applyVietnamExclusionToAllBorders() {
    try {
        const borderLayerIds = ['Other border', 'Other border dash', 'Disputed border', 'Country border'];
        
        borderLayerIds.forEach(layerId => {
            try {
                const layer = map.getLayer(layerId);
                if (layer) {
                    const currentFilter = layer.filter;
                    let hasVietnamExclusion = false;
                    
                    // Kiểm tra xem đã có filter loại trừ Vietnam chưa
                    if (Array.isArray(currentFilter)) {
                        hasVietnamExclusion = currentFilter.some(condition => 
                            Array.isArray(condition) && 
                            condition[0] === '!=' && 
                            condition[1] === 'iso_a2' && 
                            condition[2] === 'VN'
                        );
                    }
                    
                    // Nếu chưa có, thêm vào
                    if (!hasVietnamExclusion) {
                        const newFilter = [
                            'all',
                            currentFilter || ['all'],
                            ['!=', 'iso_a2', 'VN']
                        ];
                        map.setFilter(layerId, newFilter);
                        console.log(`🇻🇳 Added Vietnam exclusion to ${layerId} layer`);
                    }
                }
            } catch (e) {
                console.log(`Could not update filter for ${layerId}:`, e.message);
            }
        });
        
        console.log('🇻🇳 Applied Vietnam exclusion to all border layers');
    } catch (e) {
        console.log('Could not apply Vietnam exclusion to borders:', e.message);
    }
}

// Hàm ẩn border MapTiler admin_level=4 (tỉnh/thành)
function hideMapTilerStateBorders() {
    try {
        if (map.getLayer('state-boundaries')) {
            map.setLayoutProperty('state-boundaries', 'visibility', 'none');
            console.log('🗺️ Hidden MapTiler state borders');
        }
    } catch (e) {
        console.log('Could not hide MapTiler state borders:', e.message);
    }
}

// Hàm hiển thị border MapTiler admin_level=4 (tỉnh/thành)
function showMapTilerStateBorders() {
    try {
        if (map.getLayer('state-boundaries')) {
            map.setLayoutProperty('state-boundaries', 'visibility', 'visible');
            console.log('✅ Shown MapTiler state borders (should exclude VN)');

            // Debug: check filter
            const layer = map.getLayer('state-boundaries');
            console.log('🔍 state-boundaries filter:', layer.filter);
        } else {
            console.log('❌ state-boundaries layer not found!');
        }
    } catch (e) {
        console.log('❌ Could not show MapTiler state borders:', e.message);
    }
}

// Hàm force refresh filter cho tất cả border layers
function forceRefreshBorderFilters() {
    try {
        console.log('🔄 Force refreshing border filters...');
        
        // Ẩn hoàn toàn các border layers của MapTiler cho VN
        const borderLayers = ['Other border', 'Other border dash', 'Disputed border'];
        
        borderLayers.forEach(layerId => {
            try {
                const layer = map.getLayer(layerId);
                if (layer) {
                    console.log(`🔄 Hiding ${layerId} layer for Vietnam`);
                    map.setLayoutProperty(layerId, 'visibility', 'none');
                }
            } catch (e) {
                console.log(`Could not hide ${layerId}:`, e.message);
            }
        });
        
        console.log('🔄 Border layers hidden for Vietnam');
    } catch (e) {
        console.log('Could not refresh border filters:', e.message);
    }
}

// ===== LOGIC QUẢN LÝ ZOOM VÀ CHUYỂN ĐỔI BORDER =====

// Hàm xử lý thay đổi zoom level
// ===== LOGIC QUẢN LÝ ZOOM VÀ CHUYỂN ĐỔI BORDER =====

// Hàm xử lý thay đổi zoom level
async function handleZoomChange() {
    const newZoomLevel = map.getZoom();

    // Chỉ thực hiện nếu zoom thay đổi đáng kể để tránh gọi liên tục
    if (Math.abs(newZoomLevel - currentZoomLevel) < 0.1 && currentBorderType !== null) {
        return;
    }

    console.log(`🟢 handleZoomChange CALLED - Zoom: ${newZoomLevel.toFixed(1)}`);
    currentZoomLevel = newZoomLevel;

    // LUÔN LUÔN áp dụng bộ lọc loại trừ Việt Nam cho các layer của MapTiler
    // để đảm bảo chúng không bao giờ hiển thị dữ liệu VN không mong muốn.
    applyVietnamExclusionToAllBorders(); 

    if (newZoomLevel < ZOOM_THRESHOLD_COUNTRY) {
        // Zoom nhỏ (< 5): Chỉ hiển thị ranh giới quốc gia
        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)} < ${ZOOM_THRESHOLD_COUNTRY}: Country-level view`);

        // Ẩn Vietnam custom borders
        hideVietnamBorders();
        hideVietnamProvinceBorders();
        // Ẩn MapTiler state borders (nhưng vẫn giữ filter VN)
        hideMapTilerStateBorders();

        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)}: Showing only country borders`);

    } else if (newZoomLevel >= ZOOM_THRESHOLD_COUNTRY && newZoomLevel < ZOOM_THRESHOLD_PROVINCE) {
        // Zoom trung bình (5-8): Hiển thị province borders
        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)}: Province-level view`);

        // Hiển thị MapTiler state borders (đã filter VN) cho non-VN countries
        console.log('🗺️ Calling showMapTilerStateBorders()...');
        showMapTilerStateBorders();

        // Load Vietnam custom province borders từ local geojson files
        console.log('🇻🇳 Calling addVietnamProvinceBorders()...');
        await addVietnamProvinceBorders();

        console.log(`✅ Zoom ${newZoomLevel.toFixed(1)}: Showing province borders (VN custom + non-VN MapTiler)`);

    } else { // newZoomLevel >= ZOOM_THRESHOLD_PROVINCE
        // Zoom lớn (> 8): Hiển thị detailed province borders
        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)} >= ${ZOOM_THRESHOLD_PROVINCE}: Detailed province view`);

        // Hiển thị MapTiler state borders cho non-VN
        showMapTilerStateBorders();
        // Load Vietnam custom province borders từ local geojson files
        await addVietnamProvinceBorders();

        console.log(`🔍 Zoom ${newZoomLevel.toFixed(1)}: Showing detailed province borders`);
    }
}

// Debounce cho zoom change
let zoomChangeTimeout = null;
function debouncedHandleZoomChange() {
    clearTimeout(zoomChangeTimeout);
    zoomChangeTimeout = setTimeout(handleZoomChange, 500);
}

// Hàm thêm layer border riêng
function addBorderLayer() {
    // Kiểm tra và thêm các layer border có sẵn của MapTiler
    const borderLayers = ['boundary', 'admin', 'country-border'];
    
    borderLayers.forEach(layerName => {
        try {
            if (map.getLayer(layerName)) {
                // Di chuyển layer border lên trên temp layer
                map.moveLayer(layerName, 'temperature-layer');
                console.log(`Moved border layer ${layerName} above temp layer`);
            }
        } catch (e) {
            console.log(`Could not move border layer ${layerName}:`, e.message);
        }
    });
}

// ✅ Debounce và tracking để tránh gọi liên tục
let borderUpdateTimeout = null;

// Initialize border management
function initializeBorderManager() {
    // Setup border management on style load với debounce
    map.on('styledata', function() {
        if (map.isStyleLoaded() && !bordersInitialized) {
            const now = Date.now();
            if (now - lastBorderUpdate > 1000) { // Chỉ update nếu đã qua 1 giây
                clearTimeout(borderUpdateTimeout);
                borderUpdateTimeout = setTimeout(() => {
                    ensureAllBordersOnTop();
                    applyVietnamExclusionToAllBorders(); // Áp dụng filter loại trừ VN
                    lastBorderUpdate = Date.now();
                    bordersInitialized = true;
                    console.log('🗺️ Borders initialized once');

                    // Khởi tạo zoom level và xử lý border theo zoom
                    currentZoomLevel = map.getZoom();
                    debouncedHandleZoomChange();
                    
                    // Tạo menu control
                    createLayerControlMenu();
                }, 100);
            }
        }
    });

    // Chỉ ensure borders một lần khi map load
    map.on('load', function() {
        if (!bordersInitialized) {
            setTimeout(() => {
                ensureAllBordersOnTop();
                applyVietnamExclusionToAllBorders(); // Áp dụng filter loại trừ VN
                bordersInitialized = true;
                console.log('🗺️ Borders initialized on map load');

                // Khởi tạo zoom level và xử lý border theo zoom
                currentZoomLevel = map.getZoom();
                debouncedHandleZoomChange();
                
                // Tạo menu control
                createLayerControlMenu();
            }, 500);
        }
    });

    // Event listener cho zoom change
    map.on('zoom', debouncedHandleZoomChange);
    map.on('zoomend', debouncedHandleZoomChange);

    console.log('🗺️ Border manager initialized with Vietnam vector tiles support');
}

// Reset border state (for manual refresh if needed)
function resetBorderState() {
    bordersInitialized = false;
    lastBorderUpdate = 0;
    vietnamBordersLoaded = false;
    currentZoomLevel = 0;
    currentBorderType = null;
    console.log('🔄 Border state reset');
}

// Export functions for use in manager.js
window.borderManager = {
    ensureBoundariesOnTop,
    ensureAllBordersOnTop,
    addCustomBorderLayers,
    addBorderLayer,
    initializeBorderManager,
    resetBorderState,
    // Vietnam border functions
    addVietnamProvinceTiles,
    addVietnamCommuneTiles,
    addVietnamProvinceBorders,
    hideVietnamBorders,
    hideVietnamProvinceBorders,
    hideAllMapTilerVietnamBorders,
    hideOtherBorderForVietnam,
    showOtherBorderForNonVN,
    applyVietnamExclusionToAllBorders,
    forceRefreshBorderFilters,
    hideMapTilerStateBorders,
    showMapTilerStateBorders,
    hideMapTilerCountryBorders,
    showMapTilerCountryBorders,
    handleZoomChange,
    // Test functions
    testTilesURL,
    // Menu functions
    createLayerControlMenu,
    // Getters for state
    getVietnamBordersLoaded: () => vietnamBordersLoaded,
    getCurrentZoomLevel: () => currentZoomLevel,
    getCurrentBorderType: () => currentBorderType,
    getZoomThresholdCountry: () => ZOOM_THRESHOLD_COUNTRY,
    getZoomThresholdProvince: () => ZOOM_THRESHOLD_PROVINCE
};
